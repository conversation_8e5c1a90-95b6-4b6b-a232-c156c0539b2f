"""
Test cases for model service.
"""
import pytest
from fastapi.testclient import TestClient
import json
import os
import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.main import app

client = TestClient(app)

def test_health_endpoint():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert "version" in data
    assert "timestamp" in data

def test_metrics_endpoint():
    """Test metrics endpoint"""
    response = client.get("/metrics")
    assert response.status_code == 200
    assert b"fraud_detection_requests_total" in response.content

def test_score_endpoint_valid_data():
    """Test score endpoint with valid data"""
    test_data = {
        "transactions": [
            {
                "step": 1,
                "type": "PAYMENT",
                "amount": 1000.0,
                "nameOrig": "C111",
                "oldbalanceOrg": 5000.0,
                "newbalanceOrig": 4000.0,
                "nameDest": "M111",
                "oldbalanceDest": 0.0,
                "newbalanceDest": 0.0
            },
            {
                "step": 1,
                "type": "TRANSFER",
                "amount": 2500.0,
                "nameOrig": "C112",
                "oldbalanceOrg": 2500.0,
                "newbalanceOrig": 0.0,
                "nameDest": "C222",
                "oldbalanceDest": 10000.0,
                "newbalanceDest": 12500.0
            }
        ]
    }
    
    response = client.post("/score", json=test_data)
    assert response.status_code == 200
    data = response.json()
    assert "results" in data
    assert len(data["results"]) == 2
    
    for result in data["results"]:
        assert "transaction_id" in result
        assert "risk" in result
        assert 0 <= result["risk"] <= 1

def test_score_endpoint_invalid_data():
    """Test score endpoint with invalid data"""
    test_data = {
        "transactions": [
            {
                "step": 1,
                "type": "INVALID_TYPE",  # Invalid transaction type
                "amount": 1000.0,
                "nameOrig": "C111",
                "oldbalanceOrg": 5000.0,
                "newbalanceOrig": 4000.0,
                "nameDest": "M111",
                "oldbalanceDest": 0.0,
                "newbalanceDest": 0.0
            }
        ]
    }
    
    response = client.post("/score", json=test_data)
    assert response.status_code == 422  # Validation error

def test_score_endpoint_negative_amount():
    """Test score endpoint with negative amount"""
    test_data = {
        "transactions": [
            {
                "step": 1,
                "type": "PAYMENT",
                "amount": -1000.0,  # Negative amount
                "nameOrig": "C111",
                "oldbalanceOrg": 5000.0,
                "newbalanceOrig": 4000.0,
                "nameDest": "M111",
                "oldbalanceDest": 0.0,
                "newbalanceDest": 0.0
            }
        ]
    }
    
    response = client.post("/score", json=test_data)
    assert response.status_code == 422  # Validation error
