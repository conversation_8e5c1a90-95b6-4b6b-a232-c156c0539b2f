{"python.analysis.diagnosticSeverityOverrides": {"reportIncompatibleMethodOverride": "none"}, "python.analysis.typeCheckingMode": "basic", "python.defaultInterpreterPath": "python", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.analysis.autoImportCompletions": true, "python.analysis.completeFunctionParens": true, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true}}