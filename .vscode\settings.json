{"python.analysis.diagnosticSeverityOverrides": {"reportIncompatibleMethodOverride": "none", "reportMissingImports": "information", "reportIncompatibleVariableOverride": "none", "reportOverlappingOverload": "none"}, "python.analysis.typeCheckingMode": "basic", "python.defaultInterpreterPath": "C:\\Users\\<USER>\\anaconda3\\envs\\env\\python.exe", "python.pythonPath": "C:\\Users\\<USER>\\anaconda3\\envs\\env\\python.exe", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.analysis.autoImportCompletions": true, "python.analysis.completeFunctionParens": true, "python.analysis.extraPaths": ["./model-service/src", "./ingest-service/src"], "python.analysis.include": ["./model-service/src/**", "./ingest-service/src/**"], "files.exclude": {"**/__pycache__": true, "**/*.pyc": true}}