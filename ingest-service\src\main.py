"""
Main module for the ingest service.
Handles Kafka consumer, transaction validation, enrichment, and forwarding to model service.
"""
import os
import json
import time
import logging
import asyncio
import requests
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from prometheus_client import Counter, Histogram, generate_latest

from schemas import TransactionBase

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ingest-service")

# Initialize FastAPI app
app = FastAPI(
    title="Fraud Detection Ingest Service",
    description="Service for ingesting and preprocessing transaction data",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict to specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize metrics
MESSAGES_PROCESSED = Counter('ingest_messages_processed_total', 'Total number of messages processed')
MESSAGES_INVALID = Counter('ingest_messages_invalid_total', 'Total number of invalid messages')
PROCESSING_TIME = Histogram('ingest_processing_time_seconds', 'Time spent processing message')
MODEL_REQUESTS = Counter('ingest_model_requests_total', 'Total number of requests to model service')
MODEL_ERRORS = Counter('ingest_model_errors_total', 'Total number of errors from model service')
HIGH_RISK_ALERTS = Counter('ingest_high_risk_alerts_total', 'Total number of high risk alerts')

# Configuration
KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092")
KAFKA_TOPIC_TRANSACTIONS = os.getenv("KAFKA_TOPIC_TRANSACTIONS", "transactions")
KAFKA_TOPIC_INVALID = os.getenv("KAFKA_TOPIC_INVALID", "transactions.invalid")
MODEL_SERVICE_URL = os.getenv("MODEL_SERVICE_URL", "http://localhost:8000/score")
RISK_THRESHOLD = float(os.getenv("RISK_THRESHOLD", "0.8"))
SLACK_WEBHOOK_URL = os.getenv("SLACK_WEBHOOK_URL", "")

# Global variables
consumer = None
producer = None
websocket_clients = set()

@app.on_event("startup")
async def startup_event():
    """Initialize service on startup"""
    logger.info("Starting ingest service (Kafka-free mode)")

@app.on_event("shutdown")
async def shutdown_event():
    """Stop service on shutdown"""
    logger.info("Shutting down ingest service")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "ok",
        "version": "1.0.0",
        "timestamp": datetime.now()
    }

@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return generate_latest()

def enrich_transaction(transaction: Dict[str, Any]) -> Dict[str, Any]:
    """Enrich transaction with additional features"""
    enriched = transaction.copy()

    # Add merchant flag (if destination starts with 'M')
    enriched['merchantFlag'] = 1 if transaction.get('nameDest', '').startswith('M') else 0

    # One-hot encode transaction type
    tx_type = transaction.get('type', '')
    enriched['type_CASH_OUT'] = 1 if tx_type == 'CASH_OUT' else 0
    enriched['type_DEBIT'] = 1 if tx_type == 'DEBIT' else 0
    enriched['type_PAYMENT'] = 1 if tx_type == 'PAYMENT' else 0
    enriched['type_TRANSFER'] = 1 if tx_type == 'TRANSFER' else 0

    # Calculate balance differences
    enriched['balanceDiffOrig'] = transaction.get('oldbalanceOrg', 0) - transaction.get('newbalanceOrig', 0)
    enriched['balanceDiffDest'] = transaction.get('newbalanceDest', 0) - transaction.get('oldbalanceDest', 0)

    return enriched

@app.post("/process")
async def process_transaction(transaction: TransactionBase):
    """Process a single transaction"""
    start_time = time.time()
    MESSAGES_PROCESSED.inc()

    try:
        logger.info(f"Processing transaction: {transaction.dict()}")

        # Enrich transaction
        enriched_transaction = enrich_transaction(transaction.dict())

        # Forward to model service
        try:
            MODEL_REQUESTS.inc()
            response = requests.post(
                MODEL_SERVICE_URL,
                json={"transactions": [enriched_transaction]},
                timeout=10
            )
            response.raise_for_status()
            result = response.json()

            risk_score = result["results"][0]["risk"] if result["results"] else 0.0

            # Check for high risk
            if risk_score >= RISK_THRESHOLD:
                HIGH_RISK_ALERTS.inc()
                await send_alert(enriched_transaction, risk_score)

            # Broadcast to websocket clients
            await broadcast_transaction(enriched_transaction, risk_score)

            return {
                "status": "processed",
                "transaction_id": transaction.nameOrig,
                "risk_score": risk_score,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calling model service: {str(e)}")
            MODEL_ERRORS.inc()
            raise HTTPException(status_code=500, detail=f"Model service error: {str(e)}")

    except Exception as e:
        logger.error(f"Error processing transaction: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing error: {str(e)}")

    finally:
        processing_time = time.time() - start_time
        PROCESSING_TIME.observe(processing_time)

async def send_alert(transaction: Dict[str, Any], risk_score: float):
    """Send alert for high-risk transaction"""
    if not SLACK_WEBHOOK_URL:
        logger.info(f"High risk transaction detected (risk={risk_score}), but no webhook URL configured")
        return

    try:
        message = {
            "text": f"🚨 *HIGH RISK TRANSACTION DETECTED*",
            "blocks": [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": "🚨 HIGH RISK TRANSACTION DETECTED"
                    }
                },
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Transaction ID:*\n{transaction.get('transaction_id', 'Unknown')}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Risk Score:*\n{risk_score:.4f}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Amount:*\n${transaction.get('amount', 0):,.2f}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Type:*\n{transaction.get('type', 'Unknown')}"
                        }
                    ]
                }
            ]
        }

        response = requests.post(
            SLACK_WEBHOOK_URL,
            json=message
        )
        response.raise_for_status()
        logger.info(f"Alert sent for high risk transaction (risk={risk_score})")

    except Exception as e:
        logger.error(f"Error sending alert: {str(e)}")

@app.websocket("/ws/txns")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time transaction updates"""
    await websocket.accept()
    websocket_clients.add(websocket)

    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
    except Exception:
        # Client disconnected
        pass
    finally:
        websocket_clients.discard(websocket)

async def broadcast_transaction(transaction: Dict[str, Any], risk_score: float):
    """Broadcast transaction to all connected WebSocket clients"""
    if not websocket_clients:
        return

    # Prepare message
    message = {
        "transaction": transaction,
        "risk_score": risk_score,
        "timestamp": datetime.now().isoformat()
    }

    # Send to all clients
    for client in websocket_clients.copy():
        try:
            await client.send_json(message)
        except Exception:
            # Client may have disconnected
            websocket_clients.discard(client)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9000)
