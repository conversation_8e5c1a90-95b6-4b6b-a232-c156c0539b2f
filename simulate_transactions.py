"""
Transaction simulation script for the fraud detection platform.
Generates realistic transaction data and sends it to the ingest service.
"""
import random
import time
import requests
from datetime import datetime
import json

# Configuration
INGEST_SERVICE_URL = "http://localhost:9000/process"
DASHBOARD_URL = "http://localhost:3000/api/transactions"

# Transaction types and their fraud probabilities
TRANSACTION_TYPES = {
    'PAYMENT': 0.05,      # 5% fraud rate
    'TRANSFER': 0.15,     # 15% fraud rate  
    'CASH_OUT': 0.25,     # 25% fraud rate
    'DEBIT': 0.02         # 2% fraud rate
}

# Account prefixes
CUSTOMER_PREFIXES = ['C', 'U', 'A']
MERCHANT_PREFIXES = ['M', 'S', 'B']

def generate_account_id(prefix_list):
    """Generate a random account ID"""
    prefix = random.choice(prefix_list)
    number = random.randint(*********, *********)
    return f"{prefix}{number}"

def generate_transaction():
    """Generate a realistic transaction"""
    tx_type = random.choice(list(TRANSACTION_TYPES.keys()))
    
    # Generate accounts
    orig_account = generate_account_id(CUSTOMER_PREFIXES)
    
    # Destination depends on transaction type
    if tx_type in ['PAYMENT', 'CASH_OUT']:
        dest_account = generate_account_id(MERCHANT_PREFIXES)
    else:
        dest_account = generate_account_id(CUSTOMER_PREFIXES)
    
    # Generate amounts based on transaction type
    if tx_type == 'PAYMENT':
        amount = random.uniform(10, 5000)
    elif tx_type == 'TRANSFER':
        amount = random.uniform(100, 50000)
    elif tx_type == 'CASH_OUT':
        amount = random.uniform(50, 20000)
    else:  # DEBIT
        amount = random.uniform(20, 2000)
    
    # Generate balances
    old_balance_orig = random.uniform(amount, amount * 10)
    new_balance_orig = old_balance_orig - amount
    
    # Destination balances
    old_balance_dest = random.uniform(0, 100000)
    new_balance_dest = old_balance_dest + amount
    
    # Introduce some fraud patterns
    fraud_probability = TRANSACTION_TYPES[tx_type]
    
    # High amount transactions are more likely to be fraud
    if amount > 10000:
        fraud_probability *= 2
    
    # Round amounts
    if random.random() < fraud_probability:
        # Make it look more suspicious
        if random.random() < 0.3:
            amount = round(amount, -2)  # Round to nearest 100
        if random.random() < 0.2:
            new_balance_orig = 0  # Empty the account
    
    return {
        'step': random.randint(1, 744),  # Hour of month
        'type': tx_type,
        'amount': round(amount, 2),
        'nameOrig': orig_account,
        'oldbalanceOrg': round(old_balance_orig, 2),
        'newbalanceOrig': round(new_balance_orig, 2),
        'nameDest': dest_account,
        'oldbalanceDest': round(old_balance_dest, 2),
        'newbalanceDest': round(new_balance_dest, 2)
    }

def send_transaction(transaction):
    """Send transaction to the ingest service"""
    try:
        response = requests.post(
            INGEST_SERVICE_URL,
            json=transaction,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            risk_score = result['risk_score']
            
            print(f"✅ Transaction {result['transaction_id']}")
            print(f"   Type: {transaction['type']}, Amount: ${transaction['amount']:,.2f}")
            print(f"   Risk Score: {risk_score:.3f} ({risk_score*100:.1f}%)")
            
            if risk_score >= 0.8:
                print(f"   🚨 HIGH RISK ALERT!")
            elif risk_score >= 0.5:
                print(f"   ⚠️  Medium Risk")
            else:
                print(f"   ✅ Low Risk")
            
            print()
            return True
        else:
            print(f"❌ Failed to process transaction: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending transaction: {str(e)}")
        return False

def simulate_batch(count=10, delay=2):
    """Simulate a batch of transactions"""
    print(f"🚀 Starting simulation of {count} transactions...")
    print(f"   Delay between transactions: {delay} seconds")
    print(f"   Target: {INGEST_SERVICE_URL}")
    print("=" * 60)
    
    successful = 0
    high_risk_count = 0
    
    for i in range(count):
        print(f"Transaction {i+1}/{count}:")
        
        transaction = generate_transaction()
        
        if send_transaction(transaction):
            successful += 1
        
        # Add delay between transactions
        if i < count - 1:
            time.sleep(delay)
    
    print("=" * 60)
    print(f"📊 Simulation Complete!")
    print(f"   Successful: {successful}/{count}")
    print(f"   Success Rate: {successful/count*100:.1f}%")

def simulate_continuous(transactions_per_minute=5):
    """Simulate continuous transaction flow"""
    print(f"🔄 Starting continuous simulation...")
    print(f"   Rate: {transactions_per_minute} transactions per minute")
    print(f"   Press Ctrl+C to stop")
    print("=" * 60)
    
    delay = 60 / transactions_per_minute
    count = 0
    
    try:
        while True:
            count += 1
            print(f"Transaction #{count}:")
            
            transaction = generate_transaction()
            send_transaction(transaction)
            
            time.sleep(delay)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Simulation stopped after {count} transactions")

if __name__ == "__main__":
    print("🛡️  Fraud Detection Platform - Transaction Simulator")
    print("=" * 60)
    
    # Check if services are running
    try:
        health_response = requests.get("http://localhost:9000/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ Ingest service is running")
        else:
            print("❌ Ingest service is not responding")
            exit(1)
    except:
        print("❌ Cannot connect to ingest service")
        exit(1)
    
    print("\nChoose simulation mode:")
    print("1. Batch simulation (10 transactions)")
    print("2. Continuous simulation (5 transactions/minute)")
    print("3. Quick demo (5 transactions, 1 second delay)")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        simulate_batch(10, 2)
    elif choice == "2":
        simulate_continuous(5)
    elif choice == "3":
        simulate_batch(5, 1)
    else:
        print("Invalid choice. Running quick demo...")
        simulate_batch(5, 1)
