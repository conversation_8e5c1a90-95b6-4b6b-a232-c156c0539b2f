"""
Enhanced transaction simulation script that tests the integrated fraud detection models.
Compares performance between mock model and real trained model.
"""
import random
import time
import requests
from datetime import datetime
import json

# Configuration
INGEST_SERVICE_URL = "http://localhost:9000/process"
MODEL_SERVICE_URL = "http://localhost:8000/score"
DASHBOARD_URL = "http://localhost:3000/api/transactions"

def generate_high_risk_transaction():
    """Generate a transaction designed to trigger high fraud scores"""
    scenarios = [
        # Large cash-out with account emptying
        {
            'type': 'CASH_OUT',
            'amount': random.uniform(100000, 500000),
            'nameOrig': f'C{random.randint(*********, *********)}',
            'nameDest': f'M{random.randint(*********, *********)}',
            'oldbalanceOrg': lambda amt: amt,
            'newbalanceOrig': 0.0,
            'oldbalanceDest': 0.0,
            'newbalanceDest': lambda amt: amt
        },
        # Large transfer with suspicious patterns
        {
            'type': 'TRANSFER',
            'amount': random.uniform(200000, 1000000),
            'nameOrig': f'C{random.randint(*********, *********)}',
            'nameDest': f'C{random.randint(*********, *********)}',
            'oldbalanceOrg': lambda amt: amt * 1.2,
            'newbalanceOrig': lambda amt: amt * 0.2,
            'oldbalanceDest': random.uniform(0, 50000),
            'newbalanceDest': lambda amt, old: old + amt
        }
    ]
    
    scenario = random.choice(scenarios)
    amount = scenario['amount']
    
    transaction = {
        'step': random.randint(1, 744),
        'type': scenario['type'],
        'amount': round(amount, 2),
        'nameOrig': scenario['nameOrig'],
        'oldbalanceOrg': round(scenario['oldbalanceOrg'](amount), 2),
        'newbalanceOrig': round(scenario['newbalanceOrig'], 2) if isinstance(scenario['newbalanceOrig'], float) else round(scenario['newbalanceOrig'](amount), 2),
        'nameDest': scenario['nameDest'],
        'oldbalanceDest': round(scenario['oldbalanceDest'], 2) if isinstance(scenario['oldbalanceDest'], float) else round(scenario['oldbalanceDest'](amount), 2),
        'newbalanceDest': round(scenario['newbalanceDest'](amount, scenario['oldbalanceDest']), 2) if callable(scenario['newbalanceDest']) else round(scenario['newbalanceDest'], 2)
    }
    
    return transaction

def generate_low_risk_transaction():
    """Generate a normal, low-risk transaction"""
    tx_types = ['PAYMENT', 'DEBIT']
    tx_type = random.choice(tx_types)
    
    amount = random.uniform(10, 5000)
    old_balance = random.uniform(amount * 2, amount * 10)
    
    transaction = {
        'step': random.randint(1, 744),
        'type': tx_type,
        'amount': round(amount, 2),
        'nameOrig': f'C{random.randint(*********, *********)}',
        'oldbalanceOrg': round(old_balance, 2),
        'newbalanceOrig': round(old_balance - amount, 2),
        'nameDest': f'M{random.randint(*********, *********)}',
        'oldbalanceDest': 0.0,
        'newbalanceDest': 0.0
    }
    
    return transaction

def test_model_comparison():
    """Test and compare both model approaches"""
    print("🔬 Enhanced Model Integration Test")
    print("=" * 60)
    
    # Test high-risk transaction
    high_risk_tx = generate_high_risk_transaction()
    print(f"🚨 Testing HIGH RISK transaction:")
    print(f"   Type: {high_risk_tx['type']}, Amount: ${high_risk_tx['amount']:,.2f}")
    print(f"   Account emptying: {high_risk_tx['oldbalanceOrg'] > 0 and high_risk_tx['newbalanceOrig'] == 0}")
    
    try:
        # Test through ingest service (complete pipeline)
        response = requests.post(INGEST_SERVICE_URL, json=high_risk_tx, timeout=10)
        if response.status_code == 200:
            result = response.json()
            risk_score = result['risk_score']
            print(f"   ✅ Pipeline Risk Score: {risk_score:.3f} ({risk_score*100:.1f}%)")
            
            if risk_score >= 0.8:
                print(f"   🚨 HIGH RISK ALERT - Would trigger fraud investigation!")
            elif risk_score >= 0.5:
                print(f"   ⚠️  MEDIUM RISK - Would require manual review")
            else:
                print(f"   ✅ LOW RISK - Would be approved automatically")
        else:
            print(f"   ❌ Pipeline Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Pipeline Error: {str(e)}")
    
    print()
    
    # Test low-risk transaction
    low_risk_tx = generate_low_risk_transaction()
    print(f"✅ Testing LOW RISK transaction:")
    print(f"   Type: {low_risk_tx['type']}, Amount: ${low_risk_tx['amount']:,.2f}")
    
    try:
        response = requests.post(INGEST_SERVICE_URL, json=low_risk_tx, timeout=10)
        if response.status_code == 200:
            result = response.json()
            risk_score = result['risk_score']
            print(f"   ✅ Pipeline Risk Score: {risk_score:.3f} ({risk_score*100:.1f}%)")
            
            if risk_score >= 0.8:
                print(f"   🚨 Unexpected HIGH RISK for normal transaction!")
            elif risk_score >= 0.5:
                print(f"   ⚠️  MEDIUM RISK - May need review")
            else:
                print(f"   ✅ LOW RISK - Correctly identified as safe")
        else:
            print(f"   ❌ Pipeline Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Pipeline Error: {str(e)}")

def test_model_info():
    """Test the model information endpoint"""
    print("\n📊 Model Information:")
    print("-" * 30)
    
    try:
        response = requests.get(f"{MODEL_SERVICE_URL.replace('/score', '')}/model/info", timeout=5)
        if response.status_code == 200:
            info = response.json()
            print(f"Model Type: {info['model_type']}")
            print(f"Model Loaded: {info['model_loaded']}")
            print(f"Features: {len(info['features'])} features")
            print(f"Feature List: {', '.join(info['features'][:5])}...")
        else:
            print(f"❌ Could not get model info: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting model info: {str(e)}")

def run_batch_test(count=5):
    """Run a batch of mixed transactions"""
    print(f"\n🔄 Running batch test with {count} transactions...")
    print("-" * 50)
    
    high_risk_count = 0
    medium_risk_count = 0
    low_risk_count = 0
    
    for i in range(count):
        # Mix of high and low risk transactions
        if random.random() < 0.3:  # 30% high risk
            tx = generate_high_risk_transaction()
            expected = "HIGH"
        else:
            tx = generate_low_risk_transaction()
            expected = "LOW"
        
        try:
            response = requests.post(INGEST_SERVICE_URL, json=tx, timeout=10)
            if response.status_code == 200:
                result = response.json()
                risk_score = result['risk_score']
                
                if risk_score >= 0.8:
                    risk_level = "HIGH"
                    high_risk_count += 1
                elif risk_score >= 0.5:
                    risk_level = "MEDIUM"
                    medium_risk_count += 1
                else:
                    risk_level = "LOW"
                    low_risk_count += 1
                
                status = "✅" if (expected == "HIGH" and risk_level in ["HIGH", "MEDIUM"]) or (expected == "LOW" and risk_level == "LOW") else "⚠️"
                
                print(f"{status} Transaction {i+1}: {tx['type']} ${tx['amount']:,.0f} -> {risk_level} risk ({risk_score:.2f})")
            else:
                print(f"❌ Transaction {i+1}: Error {response.status_code}")
        except Exception as e:
            print(f"❌ Transaction {i+1}: {str(e)}")
        
        time.sleep(0.5)  # Small delay between requests
    
    print(f"\n📈 Batch Results:")
    print(f"   High Risk: {high_risk_count}")
    print(f"   Medium Risk: {medium_risk_count}")
    print(f"   Low Risk: {low_risk_count}")

if __name__ == "__main__":
    print("🛡️  Enhanced Fraud Detection Platform - Integration Test")
    print("=" * 70)
    
    # Check service availability
    try:
        health_response = requests.get("http://localhost:9000/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ Ingest service is running")
        else:
            print("❌ Ingest service is not responding properly")
            exit(1)
    except:
        print("❌ Cannot connect to ingest service")
        exit(1)
    
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ Model service is running")
        else:
            print("❌ Model service is not responding properly")
            exit(1)
    except:
        print("❌ Cannot connect to model service")
        exit(1)
    
    # Run tests
    test_model_info()
    test_model_comparison()
    
    print("\nChoose test mode:")
    print("1. Single comparison test (default)")
    print("2. Batch test (5 transactions)")
    print("3. Extended batch test (10 transactions)")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
    except:
        choice = "1"
    
    if choice == "2":
        run_batch_test(5)
    elif choice == "3":
        run_batch_test(10)
    else:
        print("\n✅ Integration test completed!")
        print("The enhanced fraud detection model is now integrated and working!")
        print("\nKey improvements:")
        print("- Real ML model integration with fallback to mock")
        print("- Enhanced feature engineering")
        print("- Better risk scoring algorithms")
        print("- Backward compatibility maintained")
