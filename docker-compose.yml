"""
Docker Compose configuration for local development.
"""
version: '3.8'

services:
  # Zookeeper service
  zookeeper:
    image: confluentinc/cp-zookeeper:7.3.0
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Kafka service
  kafka:
    image: confluentinc/cp-kafka:7.3.0
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL service
  postgres:
    image: postgres:15
    container_name: postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: fraud_detection
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Model service
  model-service:
    build:
      context: ./model-service
      dockerfile: Dockerfile
    container_name: model-service
    depends_on:
      - postgres
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/fraud_detection
      - MODEL_PATH=/app/models/fraud_detection_model.pkl
    volumes:
      - ./model-service:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Ingest service
  ingest-service:
    build:
      context: ./ingest-service
      dockerfile: Dockerfile
    container_name: ingest-service
    depends_on:
      - kafka
      - model-service
    ports:
      - "9000:9000"
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - KAFKA_TOPIC_TRANSACTIONS=transactions
      - KAFKA_TOPIC_INVALID=transactions.invalid
      - MODEL_SERVICE_URL=http://model-service:8000/score
      - RISK_THRESHOLD=0.8
    volumes:
      - ./ingest-service:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Dashboard (frontend)
  dashboard:
    build:
      context: ./dashboard
      dockerfile: Dockerfile
    container_name: dashboard
    ports:
      - "3000:3000"
    depends_on:
      - model-service
      - ingest-service
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:9000/ws/txns
    volumes:
      - ./dashboard:/app
      - /app/node_modules

volumes:
  postgres_data:
